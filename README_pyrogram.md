# بوت نقل الأعضاء - Pyrogram

تم تحويل الكود من Telethon إلى Pyrogram مع تحسينات وإضافات جديدة.

## المتطلبات

```bash
pip install pyrogram
```

## الإعداد

1. احصل على `api_id` و `api_hash` من [my.telegram.org](https://my.telegram.org)
2. ضع القيم في الكود:
```python
api_id = 827439  # ضع api_id الخاص بك
api_hash = 'ايبي هاش'  # ضع api_hash الخاص بك
```

## الأوامر

### `.نقل [رابط_المصدر] [رابط_الهدف]`
- يبدأ عملية سحب الأعضاء من المجموعة المصدر ونقلهم للمجموعة الهدف
- مثال: `.نقل @source_group @target_group`

### `.ايقاف`
- يوقف العملية الحالية (سحب أو نقل)

### `.بدء`
- يبدأ عملية النقل فقط (بدون سحب جديد)
- يستخدم الأعضاء المحفوظين من عملية سحب سابقة

## التحسينات الجديدة في Pyrogram

### 1. استخدام Pyrogram بدلاً من Telethon
- **قبل (Telethon):**
```python
from telethon.sync import TelegramClient, events
from telethon.errors.rpcerrorlist import FloodWaitError

cl = TelegramClient('sn', ai, ah)

@cl.on(events.NewMessage(pattern=r'\.نقل'))
async def _(event):
    await cl.get_entity(group)
    await cl(InviteToChannelRequest(target, [user]))
```

- **بعد (Pyrogram):**
```python
from pyrogram import Client, filters
from pyrogram.errors import FloodWait

app = Client("member_transfer", api_id=api_id, api_hash=api_hash)

@app.on_message(filters.me)
async def message_handler(client, message):
    await app.get_chat(group)
    await app.add_chat_members(target_chat.id, username)
```

### 2. معالجة أفضل للأخطاء
- **FloodWait**: انتظار تلقائي عند الوصول لحد السرعة
- **UserPrivacyRestricted**: تخطي المستخدمين ذوي الخصوصية المقيدة
- **PeerFlood**: انتظار أطول عند حظر الإضافة

### 3. تحسينات الأداء
- استخدام `asyncio.Queue` لتوزيع المهام
- معالجة متوازية للرسائل
- حفظ تلقائي للتقدم

### 4. واجهة أفضل
- رسائل حالة محدثة باستمرار
- عدادات دقيقة للأعضاء المنقولين
- تقارير مفصلة عن النتائج

## الفروق الرئيسية

| الميزة | Telethon | Pyrogram |
|--------|----------|----------|
| سهولة الاستخدام | متوسطة | عالية |
| الأداء | جيد | ممتاز |
| معالجة الأخطاء | أساسية | متقدمة |
| التوثيق | جيد | ممتاز |
| حجم المكتبة | كبير | صغير |

## مثال على الاستخدام

1. شغل البوت:
```bash
python نقل_اعضاء_pyrogram.py
```

2. في تليجرام، أرسل:
```
.نقل @my_source_group @my_target_group
```

3. انتظر انتهاء السحب والنقل، أو استخدم:
```
.ايقاف  # لإيقاف العملية
.بدء    # لبدء النقل فقط
```

## ملاحظات مهمة

- تأكد من أن لديك صلاحيات إدارة في المجموعة الهدف
- البوت يحفظ التقدم تلقائياً في ملفات
- يمكن إيقاف واستكمال العملية في أي وقت
- يتعامل مع حدود تليجرام تلقائياً

## الملفات المؤقتة

- `users.txt`: قائمة الأعضاء المستخرجة
- `target_group.txt`: رابط المجموعة الهدف
- `temp_*.txt`: ملفات مؤقتة للمعالجة

## استكشاف الأخطاء

### خطأ في API
تأكد من صحة `api_id` و `api_hash`

### خطأ في الصلاحيات
تأكد من أنك مشرف في المجموعة الهدف

### خطأ FloodWait
البوت ينتظر تلقائياً، لا تقلق

### خطأ في الروابط
استخدم الروابط الصحيحة للمجموعات (@username أو الرابط الكامل)
