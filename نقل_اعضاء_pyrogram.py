from pyrogram import Client, filters
from pyrogram.errors import <PERSON><PERSON>ait, PeerFlood, UserPrivacyRestricted, UserNotParticipant, ChatAdminRequired
import asyncio
import os
import traceback
from config import *


# متغيرات عامة
stop_flag = False
active_task = None
lock = asyncio.Lock()
total_members = 0

last_transfer = None
last_scrape = None

tasks = []
queue = None

async def safe_edit(message, new_text):
    """تحديث الرسالة بأمان"""
    try:
        if message.text != new_text:
            await message.edit_text(new_text)
    except:
        pass

async def fetch_usernames(client, chat_id, usernames_set, status_message, offset_id=None, limit=1000):
    """جلب أسماء المستخدمين من المحادثات"""
    try:
        count = 0
        async for message in client.get_chat_history(chat_id, offset_id=offset_id, limit=limit):
            if stop_flag:
                break
            if message.from_user and message.from_user.username:
                async with lock:
                    usernames_set.add(message.from_user.username)
                    count = len(usernames_set)
            await safe_edit(status_message, f"🌀 جاري السحب...\n👥 تم العثور على: {count} عضو")
            await asyncio.sleep(0.3)
    except Exception as e:
        print(f"❌ خطأ في جلب الأسماء: {e}")

async def fetch_all_usernames(client, group_link, status_message):
    """جلب جميع أسماء المستخدمين من المجموعة"""
    usernames_set = set()
    global total_members, queue, tasks

    try:
        chat = await client.get_chat(group_link)

        # الحصول على عدد الأعضاء
        total_members = await client.get_chat_members_count(chat.id)

        # الحصول على آخر رسالة لمعرفة عدد الرسائل
        total_messages = 0
        async for msg in client.get_chat_history(chat.id, limit=1):
            total_messages = msg.id
            break

        if total_messages == 0:
            await safe_edit(status_message, "❌ لا توجد رسائل في المجموعة.")
            return [], 0

        chunk_size = 50
        total_chunks = total_messages // chunk_size
        if total_messages % chunk_size != 0:
            total_chunks += 1

        max_concurrent = 20
        await safe_edit(status_message, f"📊 عدد الرسائل الكلي: {total_messages}")

        queue = asyncio.Queue()
        for i in range(total_chunks):
            offset_id = total_messages - (i * chunk_size)
            queue.put_nowait((offset_id, chunk_size))

        async def worker():
            while not queue.empty():
                if stop_flag:
                    break
                try:
                    offset_id, limit = await queue.get()
                    await fetch_usernames(client, chat.id, usernames_set, status_message, offset_id=offset_id, limit=limit)
                except Exception as e:
                    print(f"⚠️ خطأ في العامل:\n{e}")
                finally:
                    queue.task_done()

        worker_tasks = [asyncio.create_task(worker()) for _ in range(max_concurrent)]
        tasks.extend(worker_tasks)

        await queue.join()
        for task in worker_tasks:
            task.cancel()

        if not usernames_set:
            await safe_edit(status_message, "❌ لم يتم العثور على أي أعضاء، سيتم إيقاف العملية.")
            return [], 0

        return list(usernames_set), len(usernames_set)

    except Exception as e:
        await safe_edit(status_message, f"❌ خطأ أثناء السحب:\n<code>{e}</code>")
        print(traceback.format_exc())
        return [], 0

async def save_usernames_to_files(usernames_list, file_index, chunk_size, file_names):
    """حفظ أسماء المستخدمين في ملفات مؤقتة"""
    filename = f"temp_{file_index}.txt"
    with open(filename, 'w', encoding='utf-8') as f:
        start_index = file_index * chunk_size
        end_index = min((file_index + 1) * chunk_size, len(usernames_list))
        for i in range(start_index, end_index):
            f.write(usernames_list[i] + "\n")
    file_names[file_index] = filename

async def add_members(client, target_group_link, status_message, total_found):
    """إضافة الأعضاء إلى المجموعة الهدف"""
    global stop_flag, active_task
    await asyncio.sleep(1)

    try:
        target_chat = await client.get_chat(target_group_link)
        
        with open("users.txt", 'r', encoding='utf-8') as f:
            usernames = f.read().splitlines()

        added_count = 0
        total_to_add = len(usernames)

        await safe_edit(status_message, "🚚 بدء نقل الأعضاء...")
        await asyncio.sleep(2)

        i = 0
        while i < len(usernames):
            if stop_flag:
                break
                
            username = usernames[i]
            try:
                await client.add_chat_members(target_chat.id, username)
                added_count += 1
                i += 1
                
            except UserPrivacyRestricted:
                print(f"🚫 إعدادات الخصوصية تمنع إضافة: {username}")
                i += 1
                
            except FloodWait as e:
                await safe_edit(status_message, f"⏳ تم الوصول لحد السرعة، انتظار {e.value} ثانية...")
                await asyncio.sleep(e.value + 5)
                continue
                
            except PeerFlood:
                wait_time = 60 * 10  # 10 دقائق
                await safe_edit(status_message, f"⏳ تم حظر مؤقت، انتظار {wait_time} ثانية...")
                await asyncio.sleep(wait_time)
                continue
                
            except Exception as e:
                print(f"❌ خطأ مع المستخدم {username}:\n{traceback.format_exc()}")
                i += 1

            # تحديث الملف
            remaining_usernames = usernames[i:]
            with open("users.txt", 'w', encoding='utf-8') as f:
                f.write("\n".join(remaining_usernames))

            await safe_edit(status_message, f"🚀 تم النقل: {added_count} من أصل: {total_to_add}")
            await asyncio.sleep(15)

        await safe_edit(status_message,
            f"✅ تم الانتهاء من النقل! 🎉\n"
            f"👤 الأعضاء المنقولون: {added_count}\n"
            f"👥 عدد أعضاء المجموعة الأصلية: {total_found}"
        )

    except Exception as e:
        await safe_edit(status_message, f"❌ خطأ في النقل:\n<code>{e}</code>")
        print(traceback.format_exc())

    stop_flag = False
    active_task = None

@Client.on_message(filters.me & filters.command(["نقل", "ايقاف", "بدء"], prefixes=".") & filters.user(sudo_id))
async def message_handler(client, message):
    global stop_flag, active_task, total_members, last_transfer, last_scrape, queue, tasks

    command = message.command[0] if message.command else ""

    if command == "نقل":
        if active_task:
            await safe_edit(message, "⚠️ يوجد عملية قيد التنفيذ، اكتب (.ايقاف) لإيقاف العملية الحالية.")
            return

        if len(message.command) < 3:
            await safe_edit(message, "❌ صيغة الأمر خاطئة!\nالصيغة الصحيحة: .نقل [رابط_المجموعة_المصدر] [رابط_المجموعة_الهدف]")
            return

        source_link = message.command[1]
        target_link = message.command[2]

        # حفظ رابط المجموعة الهدف
        with open("target_group.txt", 'w', encoding='utf-8') as f:
            f.write(target_link)

        # حذف الرسالة السابقة إن وجدت
        if last_transfer and last_transfer.id != message.id:
            try:
                await last_transfer.delete()
            except:
                pass
        last_transfer = message

        async def full_transfer():
            await safe_edit(message, "🌀 بدء سحب الأعضاء...")

            all_usernames, total = await fetch_all_usernames(client, source_link, message)
            if total == 0:
                return

            # تقسيم الأسماء على 5 ملفات
            file_names = [None] * 5
            chunk_size = max(1, total // 5)
            tasks_list = []

            for i in range(5):
                tasks_list.append(asyncio.create_task(save_usernames_to_files(all_usernames, i, chunk_size, file_names)))

            await asyncio.gather(*tasks_list)

            # دمج الملفات في ملف واحد
            with open("users.txt", 'w', encoding='utf-8') as output_file:
                for filename in file_names:
                    if filename and os.path.exists(filename):
                        with open(filename, 'r', encoding='utf-8') as input_file:
                            output_file.write(input_file.read())
                        os.remove(filename)

            await add_members(client, target_link, message, total_members)

        stop_flag = False
        active_task = asyncio.create_task(full_transfer())

    elif command == "ايقاف":
        if active_task:
            stop_flag = True
            
            # تنظيف الطابور
            if queue:
                while not queue.empty():
                    try:
                        queue.get_nowait()
                        queue.task_done()
                    except asyncio.QueueEmpty:
                        break

            # إلغاء المهام
            for task in tasks:
                if not task.done():
                    task.cancel()
            tasks.clear()

            await safe_edit(message, "🛑 جاري إيقاف العملية...")
            try:
                await active_task
            except:
                pass
            active_task = None
            stop_flag = False
            await safe_edit(message, "✅ تم الإيقاف بنجاح.")

            # حذف الرسائل السابقة
            if last_transfer:
                try:
                    await last_transfer.delete()
                except:
                    pass
                last_transfer = None

            if last_scrape:
                try:
                    await last_scrape.delete()
                except:
                    pass
                last_scrape = None
        else:
            await safe_edit(message, "❗ لا توجد عملية قيد التنفيذ حالياً.")

    elif command == "بدء":
        if active_task:
            stop_flag = True
            
            # تنظيف الطابور والمهام
            if queue:
                while not queue.empty():
                    try:
                        queue.get_nowait()
                        queue.task_done()
                    except asyncio.QueueEmpty:
                        break

            for task in tasks:
                if not task.done():
                    task.cancel()
            tasks.clear()

            await safe_edit(message, "⚠️ سيتم إيقاف السحب وبدء النقل...")
            try:
                await active_task
            except:
                pass
            active_task = None
            stop_flag = False

        if not os.path.exists("users.txt"):
            await safe_edit(message, "❗ يجب تنفيذ أمر .نقل أولاً")
            return
            
        with open("users.txt", 'r', encoding='utf-8') as f:
            usernames = [line.strip() for line in f if line.strip()]
            
        if not usernames:
            await safe_edit(message, "❗ الملف فارغ، لا يمكن نقل أي عضو.")
            return

        if not os.path.exists("target_group.txt"):
            await safe_edit(message, "❗ رابط المجموعة الهدف غير محفوظ، نفذ أمر .نقل أولاً.")
            return
            
        with open("target_group.txt", 'r', encoding='utf-8') as f:
            target_link = f.read().strip()

        # حذف الرسائل السابقة
        if last_transfer:
            try:
                await last_transfer.delete()
            except:
                pass
            last_transfer = None

        if last_scrape and last_scrape.id != message.id:
            try:
                await last_scrape.delete()
            except:
                pass
        last_scrape = message

        async def just_add():
            await safe_edit(message, "✅ بدء النقل")
            await add_members(client, target_link, message, total_members)

        active_task = asyncio.create_task(just_add())