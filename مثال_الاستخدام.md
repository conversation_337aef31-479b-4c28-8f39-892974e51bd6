# مثال على استخدام بوت نقل الأعضاء - Pyrogram

## الكود المحدث

تم تحديث الكود ليستخدم `@Client.on_message` مع `filters.command` كما طلبت:

```python
@app.on_message(filters.me & filters.command(["نقل", "ايقاف", "بدء"], prefixes=".") & filters.user(sudo_id))
async def message_handler(client, message):
    command = message.command[0] if message.command else ""
    
    if command == "نقل":
        # منطق أمر النقل
    elif command == "ايقاف":
        # منطق أمر الإيقاف
    elif command == "بدء":
        # منطق أمر البدء
```

## الإعدادات المطلوبة

1. **ضع معرف المطور:**
```python
sudo_id = 5978895403  # ضع معرف المطور هنا
```

2. **ضع بيانات API:**
```python
api_id = 827439  # ضع api_id الخاص بك
api_hash = 'ايبي هاش'  # ضع api_hash الخاص بك
```

## الأوامر المتاحة

### 1. أمر النقل
```
.نقل @source_group @target_group
```
**مثال:**
```
.نقل @my_source_channel @my_target_group
```

### 2. أمر الإيقاف
```
.ايقاف
```
يوقف العملية الحالية (سحب أو نقل)

### 3. أمر البدء
```
.بدء
```
يبدأ عملية النقل فقط بدون سحب جديد

## مميزات الكود الجديد

### 1. استخدام `filters.command`
- **قبل:**
```python
if text.startswith('.نقل'):
    _, source_link, target_link = text.split()
```

- **بعد:**
```python
if command == "نقل":
    source_link = message.command[1]
    target_link = message.command[2]
```

### 2. تحديد المستخدم المسموح
```python
filters.user(sudo_id)
```
فقط المستخدم المحدد يمكنه استخدام البوت

### 3. تحديد الأوامر المسموحة
```python
filters.command(["نقل", "ايقاف", "بدء"], prefixes=".")
```
فقط الأوامر المحددة ستعمل

### 4. فلترة الرسائل الشخصية
```python
filters.me
```
البوت يستجيب فقط لرسائلك الشخصية

## مثال كامل للاستخدام

1. **تشغيل البوت:**
```bash
python نقل_اعضاء_pyrogram.py
```

2. **في تليجرام، أرسل:**
```
.نقل @source_channel @target_group
```

3. **ستحصل على رد مثل:**
```
🌀 بدء سحب الأعضاء...
📊 عدد الرسائل الكلي: 15000
🌀 جاري السحب...
👥 تم العثور على: 1250 عضو
🚚 بدء نقل الأعضاء...
🚀 تم النقل: 50 من أصل: 1250
```

4. **لإيقاف العملية:**
```
.ايقاف
```

5. **لاستكمال النقل لاحقاً:**
```
.بدء
```

## معالجة الأخطاء

### خطأ في صيغة الأمر
```
❌ صيغة الأمر خاطئة!
الصيغة الصحيحة: .نقل [رابط_المجموعة_المصدر] [رابط_المجموعة_الهدف]
```

### عملية قيد التنفيذ
```
⚠️ يوجد عملية قيد التنفيذ، اكتب (.ايقاف) لإيقاف العملية الحالية.
```

### حد السرعة
```
⏳ تم الوصول لحد السرعة، انتظار 60 ثانية...
```

### حظر مؤقت
```
⏳ تم حظر مؤقت، انتظار 600 ثانية...
```

## نصائح للاستخدام

1. **استخدم روابط صحيحة:**
   - `@username` للقنوات والمجموعات العامة
   - الرابط الكامل للمجموعات الخاصة

2. **تأكد من الصلاحيات:**
   - يجب أن تكون مشرف في المجموعة الهدف
   - يجب أن تكون عضو في المجموعة المصدر

3. **كن صبوراً:**
   - العملية قد تستغرق وقتاً طويلاً
   - البوت يتعامل مع حدود تليجرام تلقائياً

4. **احفظ التقدم:**
   - البوت يحفظ التقدم في ملفات
   - يمكن إيقاف واستكمال العملية

## الملفات المُنشأة

- `users.txt` - قائمة الأعضاء المستخرجة
- `target_group.txt` - رابط المجموعة الهدف
- `temp_*.txt` - ملفات مؤقتة (تُحذف تلقائياً)

## الأمان

- البوت يعمل فقط مع معرف المطور المحدد
- لا يستجيب لأي مستخدم آخر
- يحفظ البيانات محلياً فقط
