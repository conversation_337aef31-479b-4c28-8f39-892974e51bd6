from pyrogram import Client, filters
from pyrogram.errors import FloodWait, PeerFlood, UserPrivacyRestricted
import asyncio
import os
import traceback

ai = 827439 #ايبي ايدي
ah = 'ايبي هاش' #ايبي هاش

cl = Client("sn", api_id=ai, api_hash=ah)

sr = False
at = None
lk = asyncio.Lock()
tm = 0

lt = None
ls = None

tt = []
qe = None

async def se(mo, nt):
    try:
        if mo.text != nt:
            await mo.edit_text(nt)
    except:
        pass

async def fo(gp, us, mo, oi=None, lm=1000):
    try:
        ct = 0
        async for mg in cl.get_chat_history(gp, offset_id=oi, limit=lm):
            if sr:
                break
            if mg.from_user and mg.from_user.username:
                async with lk:
                    us.add(mg.from_user.username)
                    ct = len(us)
            await se(mo, f"🌀 دنسحب...\n👥 لكينه: {ct} عضو")
            await asyncio.sleep(0.3)
    except Exception as e:
        print(f"❌ خيط السحب فشل: {e}")

async def fu(gl, mo):
    us = set()
    global tm, qe, tt

    try:
        gp = await cl.get_chat(gl)

        # الحصول على عدد الأعضاء
        tm = await cl.get_chat_members_count(gp.id)

        # الحصول على آخر رسالة لمعرفة عدد الرسائل
        total_msg = 0
        async for msg in cl.get_chat_history(gp.id, limit=1):
            total_msg = msg.id
            break

        if total_msg == 0:
            await se(mo, "❌ لا توجد رسائل بالكروب.")
            return [], 0

        cs = 50
        tc = total_msg // cs
        if total_msg % cs != 0:
            tc += 1

        mc = 20
        await se(mo, f"📊 عدد الرسائل الكلي: {total_msg}")

        qe = asyncio.Queue()
        for i in range(tc):
            oi = total_msg - (i * cs)
            qe.put_nowait((oi, cs))

        async def wk():
            while not qe.empty():
                if sr:
                    break
                try:
                    oi, lm = await qe.get()
                    await fo(gp.id, us, mo, oi=oi, lm=lm)
                except Exception as e:
                    print(f"⚠️ خطأ داخل الخيط:\n{e}")
                finally:
                    qe.task_done()

        ts = [asyncio.create_task(wk()) for _ in range(mc)]
        tt.extend(ts)

        await qe.join()
        for ta in ts:
            ta.cancel()

        if not us:
            await se(mo, "❌ ما لكينه ولا عضو، راح نوقف العملية.")
            return [], 0

        return list(us), len(us)

    except Exception as e:
        await se(mo, f"❌ خطأ أثناء السحب:\n<code>{e}</code>")
        print(traceback.format_exc())
        return [], 0

async def su(us, ix, cs, fn):
    fnm = f"temp_{ix}.txt"
    with open(fnm, 'w') as f:
        for i in range(ix * cs, min((ix + 1) * cs, len(us))):
            f.write(us[i] + "\n")
    fn[ix] = fnm


async def am(tgl, mo, tf):
    global sr, at
    await asyncio.sleep(1)
    try:
        gp = await cl.get_chat(tgl)
        with open("users.txt", 'r') as f:
            un = f.read().splitlines()

        ad = 0
        tt_add = len(un)

        await se(mo, "🚚 راح نبدي ننقل الأعضاء...")
        await asyncio.sleep(2)

        i = 0
        while i < len(un):
            if sr:
                break
            u = un[i]
            try:
                await cl.add_chat_members(gp.id, u)
                ad += 1
                i += 1
            except UserPrivacyRestricted:
                print(f"🚫 خصوصية تمنع إضافة: {u}")
                i += 1
            except FloodWait as e:
                await se(mo, f"⏳ تم حظر مؤقت، لازم ننتظر {e.value} ثانية قبل إكمال النقل.")
                await asyncio.sleep(e.value + 5)
                continue
            except PeerFlood:
                wait_time = 60 * 10
                await se(mo, f"⏳ تم حظر مؤقت بسبب Flood، لازم ننتظر {wait_time} ثانية قبل إكمال النقل.")
                await asyncio.sleep(wait_time)
                continue
            except Exception:
                print(f"❌ خطأ وية {u}:\n{traceback.format_exc()}")
                i += 1

            un = un[i:]
            with open("users.txt", 'w') as f:
                f.write("\n".join(un))

            await se(mo, f"🚀 نقلنه: {ad} من اصل: {tt_add}")
            await asyncio.sleep(15)

        await se(mo,
            f"✅ خلصنا النقل 🎉\n"
            f"👤 الأعضاء اللي نقلناهم: {ad}\n"
            f"👥 عدد أعضاء الكروب الأصلي: {tf}"
        )

    except Exception as e:
        await se(mo, f"❌ خطأ النقل:\n<code>{e}</code>")
        print(traceback.format_exc())

    sr = False
    at = None

@cl.on_message(filters.me)
async def mh(client, message):
    global sr, at, tm, lt, ls, qe, tt

    tx = message.text.strip() if message.text else ""
    mo = message

    if tx.startswith('.نقل'):
        if at:
            await se(mo, "⚠️ بعدك مشغول، اكتب (.ايقاف) حتى توقف العملية الحالية.")
            return
        try:
            _, sl, dl = tx.split()
        except:
            await se(mo, "❌ الأمر غلط!\nالصيغة الصح: .نقل [رابط السحب] [رابط النقل]")
            return

        with open("target_group.txt", 'w') as f:
            f.write(dl)

        if lt and lt.id != mo.id:
            try:
                await lt.delete()
            except:
                pass
        lt = mo

        async def ft():
            await se(mo, "🌀 دنسحب الأعضاء...")

            all_us, tot = await fu(sl, mo)
            if tot == 0:
                return

            fn = [None] * 5
            ch = max(1, tot // 5)
            th = []

            for i in range(5):
                th.append(asyncio.create_task(su(all_us, i, ch, fn)))

            await asyncio.gather(*th)

            with open("users.txt", 'w') as out_f:
                for fl in fn:
                    with open(fl, 'r') as in_f:
                        out_f.write(in_f.read())
                    os.remove(fl)

            await am(dl, mo, tm)

        sr = False
        at = asyncio.create_task(ft())

    elif tx == '.ايقاف':
        if at:
            sr = True
            if qe:
                while not qe.empty():
                    try:
                        qe.get_nowait()
                        qe.task_done()
                    except asyncio.QueueEmpty:
                        break

            for ta in tt:
                if not ta.done():
                    ta.cancel()
            tt.clear()

            await se(mo, "🛑 دنتظر نكمل الإيقاف...")
            try:
                await at
            except:
                pass
            at = None
            sr = False
            await se(mo, "✅ تم الإيقاف بنجاح.")

            if lt:
                try:
                    await lt.delete()
                except:
                    pass
                lt = None

            if ls:
                try:
                    await ls.delete()
                except:
                    pass
                ls = None
        else:
            await se(mo, "❗ماكو عملية شغالة حالياً.")

    elif tx == '.بدء':
        if at:
            sr = True
            if qe:
                while not qe.empty():
                    try:
                        qe.get_nowait()
                        qe.task_done()
                    except asyncio.QueueEmpty:
                        break

            for ta in tt:
                if not ta.done():
                    ta.cancel()
            tt.clear()

            await se(mo, "⚠️ راح نوقف السحب ونبدي بالنقل...")
            try:
                await at
            except:
                pass
            at = None
            sr = False

        if not os.path.exists("users.txt"):
            await se(mo, "❗لازم تسوي .نقل اول")
            return
        with open("users.txt", 'r') as f:
            lsns = [l.strip() for l in f if l.strip()]
        if not lsns:
            await se(mo, "❗ فاضي، ما نكدر ننقل أحد.")
            return

        if not os.path.exists("target_group.txt"):
            await se(mo, "❗ما محفوظ رابط كروب النقل، سوي .نقل أول.")
            return
        with open("target_group.txt", 'r') as f:
            dl = f.read().strip()

        if lt:
            try:
                await lt.delete()
            except:
                pass
            lt = None

        if ls and ls.id != mo.id:
            try:
                await ls.delete()
            except:
                pass
        ls = mo

        async def ja():
            await se(mo, "✅ راح نبدي ننقل")
            await am(dl, mo, tm)

        at = asyncio.create_task(ja())

if __name__ == "__main__":
    cl.run()