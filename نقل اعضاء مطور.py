from telethon.errors.rpcerrorlist import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PeerFloodError
from telethon.sync import TelegramClient, events
from telethon.tl.functions.channels import GetFullChannelRequest, InviteToChannelRequest
from telethon import errors
import asyncio
import os
import traceback

ai = 827439 #ايبي ايدي
ah = 'ايبي هاش' #ايبي هاش

cl = TelegramClient('sn', ai, ah)

sr = False
at = None
lk = asyncio.Lock()
tm = 0

lt = None
ls = None

tt = []
qe = None

async def se(mo, nt):
    try:
        if mo.message != nt:
            await mo.edit(nt)
    except:
        pass

async def fo(gp, us, mo, oi=None, lm=1000):
    try:
        ct = 0
        async for mg in cl.iter_messages(gp, offset_id=oi, reverse=False, limit=lm):
            if sr:
                break
            if mg.sender and mg.sender.username:
                async with lk:
                    us.add(mg.sender.username)
                    ct = len(us)
            await se(mo, f"🌀 دنسحب...\n👥 لكينه: {ct} عضو")
            await asyncio.sleep(0.3)
    except Exception as e:
        print(f"❌ خيط السحب فشل: {e}")

async def fu(gl, mo):
    us = set()
    global tm, qe, tt

    try:
        gp = await cl.get_entity(gl)
        fc = await cl(GetFullChannelRequest(gp))
        total_msg = fc.full_chat.read_inbox_max_id
        tm = fc.full_chat.participants_count

        if total_msg == 0:
            await se(mo, "❌ لا توجد رسائل بالكروب.")
            return [], 0

        cs = 50
        tc = total_msg // cs
        if total_msg % cs != 0:
            tc += 1

        mc = 20
        await se(mo, f"📊 عدد الرسائل الكلي: {total_msg}")

        qe = asyncio.Queue()
        for i in range(tc):
            oi = total_msg - (i * cs)
            qe.put_nowait((oi, cs))

        async def wk():
            while not qe.empty():
                if sr:
                    break
                try:
                    oi, lm = await qe.get()
                    await fo(gp, us, mo, oi=oi, lm=lm)
                except Exception as e:
                    print(f"⚠️ خطأ داخل الخيط:\n{e}")
                finally:
                    qe.task_done()

        ts = [asyncio.create_task(wk()) for _ in range(mc)]
        tt.extend(ts)

        await qe.join()
        for ta in ts:
            ta.cancel()

        if not us:
            await se(mo, "❌ ما لكينه ولا عضو، راح نوقف العملية.")
            return [], 0

        return list(us), len(us)

    except Exception as e:
        await se(mo, f"❌ خطأ أثناء السحب:\n<code>{e}</code>")
        print(traceback.format_exc())
        return [], 0

async def su(us, ix, cs, fn):
    fnm = f"temp_{ix}.txt"
    with open(fnm, 'w') as f:
        for i in range(ix * cs, min((ix + 1) * cs, len(us))):
            f.write(us[i] + "\n")
    fn[ix] = fnm


async def am(tgl, mo, tf):
    global sr, at
    await asyncio.sleep(1)
    try:
        gp = await cl.get_entity(tgl)
        with open("users.txt", 'r') as f:
            un = f.read().splitlines()

        ad = 0
        tt_add = len(un)

        await se(mo, "🚚 راح نبدي ننقل الأعضاء...")
        await asyncio.sleep(2)

        i = 0
        while i < len(un):
            if sr:
                break
            u = un[i]
            try:
                await cl(InviteToChannelRequest(gp, [u]))
                ad += 1
                i += 1
            except errors.UserPrivacyRestrictedError:
                print(f"🚫 خصوصية تمنع إضافة: {u}")
                i += 1
            except FloodWaitError as e:
                await se(mo, f"⏳ تم حظر مؤقت، لازم ننتظر {e.seconds} ثانية قبل إكمال النقل.")
                await asyncio.sleep(e.seconds + 5)
                continue
            except PeerFloodError:
                wait_time = 60 * 10  
                await se(mo, f"⏳ تم حظر مؤقت بسبب Flood، لازم ننتظر {wait_time} ثانية قبل إكمال النقل.")
                await asyncio.sleep(wait_time)
                continue
            except Exception:
                print(f"❌ خطأ وية {u}:\n{traceback.format_exc()}")
                i += 1

            un = un[i:]
            with open("users.txt", 'w') as f:
                f.write("\n".join(un))

            await se(mo, f"🚀 نقلنه: {ad} من اصل: {tt_add}")
            await asyncio.sleep(15)

        await se(mo,
            f"✅ خلصنا النقل 🎉\n"
            f"👤 الأعضاء اللي نقلناهم: {ad}\n"
            f"👥 عدد أعضاء الكروب الأصلي: {tf}"
        )

    except Exception as e:
        await se(mo, f"❌ خطأ النقل:\n<code>{e}</code>")
        print(traceback.format_exc())

    sr = False
    at = None

@cl.on(events.NewMessage(chats='me'))
async def mh(ev):
    global sr, at, tm, lt, ls, qe, tt

    tx = ev.raw_text.strip()
    mo = ev.message

    if tx.startswith('.نقل'):
        if at:
            await se(mo, "⚠️ بعدك مشغول، اكتب (.ايقاف) حتى توقف العملية الحالية.")
            return
        try:
            _, sl, dl = tx.split()
        except:
            await se(mo, "❌ الأمر غلط!\nالصيغة الصح: .نقل [رابط السحب] [رابط النقل]")
            return

        with open("target_group.txt", 'w') as f:
            f.write(dl)

        if lt and lt.id != mo.id:
            try:
                await lt.delete()
            except:
                pass
        lt = mo

        async def ft():
            await se(mo, "🌀 دنسحب الأعضاء...")

            all_us, tot = await fu(sl, mo)
            if tot == 0:
                return

            fn = [None] * 5
            ch = max(1, tot // 5)
            th = []

            for i in range(5):
                th.append(asyncio.create_task(su(all_us, i, ch, fn)))

            await asyncio.gather(*th)

            with open("users.txt", 'w') as out_f:
                for fl in fn:
                    with open(fl, 'r') as in_f:
                        out_f.write(in_f.read())
                    os.remove(fl)

            await am(dl, mo, tm)

        sr = False
        at = asyncio.create_task(ft())

    elif tx == '.ايقاف':
        if at:
            sr = True
            if qe:
                while not qe.empty():
                    try:
                        qe.get_nowait()
                        qe.task_done()
                    except asyncio.QueueEmpty:
                        break

            for ta in tt:
                if not ta.done():
                    ta.cancel()
            tt.clear()

            await se(mo, "🛑 دنتظر نكمل الإيقاف...")
            try:
                await at
            except:
                pass
            at = None
            sr = False
            await se(mo, "✅ تم الإيقاف بنجاح.")

            if lt:
                try:
                    await lt.delete()
                except:
                    pass
                lt = None

            if ls:
                try:
                    await ls.delete()
                except:
                    pass
                ls = None
        else:
            await se(mo, "❗ماكو عملية شغالة حالياً.")

    elif tx == '.بدء':
        if at:
            sr = True
            if qe:
                while not qe.empty():
                    try:
                        qe.get_nowait()
                        qe.task_done()
                    except asyncio.QueueEmpty:
                        break

            for ta in tt:
                if not ta.done():
                    ta.cancel()
            tt.clear()

            await se(mo, "⚠️ راح نوقف السحب ونبدي بالنقل...")
            try:
                await at
            except:
                pass
            at = None
            sr = False

        if not os.path.exists("users.txt"):
            await se(mo, "❗لازم تسوي .نقل اول")
            return
        with open("users.txt", 'r') as f:
            lsns = [l.strip() for l in f if l.strip()]
        if not lsns:
            await se(mo, "❗ فاضي، ما نكدر ننقل أحد.")
            return

        if not os.path.exists("target_group.txt"):
            await se(mo, "❗ما محفوظ رابط كروب النقل، سوي .نقل أول.")
            return
        with open("target_group.txt", 'r') as f:
            dl = f.read().strip()

        if lt:
            try:
                await lt.delete()
            except:
                pass
            lt = None

        if ls and ls.id != mo.id:
            try:
                await ls.delete()
            except:
                pass
        ls = mo

        async def ja():
            await se(mo, "✅ راح نبدي ننقل")
            await am(dl, mo, tm)

        at = asyncio.create_task(ja())

cl.start()
cl.run_until_disconnected()