# مقارنة بين Telethon و Pyrogram

## 1. الاستيراد والإعداد

### Telethon
```python
from telethon.errors.rpcerrorlist import FloodWaitError, PeerFloodError
from telethon.sync import TelegramClient, events
from telethon.tl.functions.channels import GetFullChannelRequest, InviteToChannelRequest
from telethon import errors

cl = TelegramClient('sn', ai, ah)
```

### Pyrogram
```python
from pyrogram import Client, filters
from pyrogram.errors import FloodWait, PeerFlood, UserPrivacyRestricted

app = Client("member_transfer", api_id=api_id, api_hash=api_hash)
```

## 2. معالجة الأحداث

### Telethon
```python
@cl.on(events.NewMessage(pattern=r'\.نقل'))
async def _(event):
    if event.sender_id != 5978895403:
        return
    message_text = event.message.message
```

### Pyrogram
```python
@app.on_message(filters.me)
async def message_handler(client, message):
    text = message.text.strip() if message.text else ""
    if text.startswith('.نقل'):
```

## 3. الحصول على معلومات المجموعة

### Telethon
```python
source_entity = await cl.get_entity(source_group)
target_entity = await cl.get_entity(target_group)
participants = await cl.get_participants(source_entity, limit=None)
```

### Pyrogram
```python
source_chat = await app.get_chat(source_group)
target_chat = await app.get_chat(target_group)
members = []
async for member in app.get_chat_members(source_chat.id):
    members.append(member)
```

## 4. إضافة الأعضاء

### Telethon
```python
await cl(InviteToChannelRequest(target_entity, [user]))
```

### Pyrogram
```python
await app.add_chat_members(target_chat.id, username)
```

## 5. معالجة الأخطاء

### Telethon
```python
except FloodWaitError as e:
    await asyncio.sleep(e.seconds)
except PeerFloodError:
    await asyncio.sleep(300)
except errors.UserPrivacyRestrictedError:
    continue
```

### Pyrogram
```python
except FloodWait as e:
    await asyncio.sleep(e.value)
except PeerFlood:
    await asyncio.sleep(300)
except UserPrivacyRestricted:
    continue
```

## 6. تحديث الرسائل

### Telethon
```python
if event.message.message != new_text:
    await event.message.edit(new_text)
```

### Pyrogram
```python
if message.text != new_text:
    await message.edit_text(new_text)
```

## 7. الحصول على تاريخ المحادثة

### Telethon
```python
async for msg in cl.iter_messages(group, offset_id=offset_id, limit=limit):
    if msg.sender and msg.sender.username:
        usernames.add(msg.sender.username)
```

### Pyrogram
```python
async for msg in app.get_chat_history(chat_id, offset_id=offset_id, limit=limit):
    if msg.from_user and msg.from_user.username:
        usernames.add(msg.from_user.username)
```

## 8. تشغيل البوت

### Telethon
```python
cl.start()
cl.run_until_disconnected()
```

### Pyrogram
```python
if __name__ == "__main__":
    app.run()
```

## المزايا والعيوب

### Telethon
**المزايا:**
- مكتبة قديمة ومستقرة
- دعم كامل لـ Telegram API
- مجتمع كبير

**العيوب:**
- صعوبة في الاستخدام
- كود أكثر تعقيداً
- معالجة أخطاء أساسية

### Pyrogram
**المزايا:**
- سهولة في الاستخدام
- كود أبسط وأوضح
- معالجة أخطاء متقدمة
- أداء أفضل
- توثيق ممتاز

**العيوب:**
- مكتبة أحدث (أقل استقراراً نسبياً)
- مجتمع أصغر

## الخلاصة

Pyrogram أفضل للمشاريع الجديدة بسبب:
1. سهولة الكتابة والقراءة
2. معالجة أخطاء أفضل
3. أداء محسن
4. توثيق أوضح

Telethon مناسب للمشاريع الموجودة أو عند الحاجة لميزات متقدمة جداً.
